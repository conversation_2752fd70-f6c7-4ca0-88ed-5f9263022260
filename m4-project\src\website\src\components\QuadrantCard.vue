<script setup>
// Define props for the component
defineProps({
  title: {
    type: String,
    default: 'Quadrant Title'
  },
  addButtonText: {
    type: String,
    default: '+Add Item'
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['classes', 'students', 'documents', 'statistics', 'default'].includes(value)
  }
})

// Sample data for different quadrant types
const classesData = [
  { id: 1, name: 'class 5', teacher: 'mr. teachername' },
  { id: 2, name: 'class 2', teacher: 'Ms. Jimmyh...' },
  { id: 3, name: 'class 1', teacher: 'Mr. owfool' }
]

const studentsData = [
  'Jimmy Lastname', 'Jimmy Lastname', 'Jimmy Lastname', 'Jimmy Lastname',
  'Jimmy Lastname', 'Jimmy Lastname', 'Jimmy Lastname', 'Jimmy Lastname'
]

const documentsData = [
  { title: 'May 2025', lastEdited: '20/3/2025', editor: 'Mr.<PERSON>quad' },
  { title: 'May 2025', lastEdited: '20/3/2025', editor: 'Mr.<PERSON>qua<PERSON>' },
  { title: 'May 2025', lastEdited: '20/3/2025', editor: 'Mr.<PERSON>quad' },
  { title: 'May 2025', lastEdited: '20/3/2025', editor: 'Mr.Farquad' }
]

const statisticsData = {
  subjects: ['math', 'reading', 'topo', 'writing'],
  classAverage: [8, 8, 8, 8],
  reachedNorm: ['yes', 'yes', 'yes', 'yes'],
  passingPerc: ['20%', '20%', '20%', '20%']
}
</script>

<template>
  <div class="quadrant-card" :class="`quadrant-${type}`">
    <div class="card-header">
      <h2 class="card-title">{{ title }}</h2>
      <button v-if="addButtonText" class="add-button">{{ addButtonText }}</button>
    </div>
    <div class="card-content">

      <!-- Classes Content -->
      <div v-if="type === 'classes'" class="classes-content">
        <div v-for="classItem in classesData" :key="classItem.id" class="class-item">
          <div class="class-name">{{ classItem.name }}</div>
          <div class="class-teacher">{{ classItem.teacher }}</div>
        </div>
      </div>

      <!-- Students Content -->
      <div v-else-if="type === 'students'" class="students-content">
        <div v-for="(student, index) in studentsData" :key="index" class="student-item">
          {{ student }}
        </div>
      </div>

      <!-- Documents Content -->
      <div v-else-if="type === 'documents'" class="documents-content">
        <div v-for="(doc, index) in documentsData" :key="index" class="document-item">
          {{ doc.title }} | Last edited {{ doc.lastEdited }} by {{ doc.editor }}
        </div>
      </div>

      <!-- Statistics Content -->
      <div v-else-if="type === 'statistics'" class="statistics-content">
        <table class="stats-table">
          <thead>
            <tr>
              <th></th>
              <th v-for="subject in statisticsData.subjects" :key="subject">{{ subject }}</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="row-label">class average</td>
              <td v-for="(avg, index) in statisticsData.classAverage" :key="index">{{ avg }}</td>
            </tr>
            <tr>
              <td class="row-label">Reached norm</td>
              <td v-for="(norm, index) in statisticsData.reachedNorm" :key="index">{{ norm }}</td>
            </tr>
            <tr>
              <td class="row-label">passing perc</td>
              <td v-for="(perc, index) in statisticsData.passingPerc" :key="index">{{ perc }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Default Content -->
      <div v-else class="default-content">
        <slot>
          <div class="placeholder-content">
            <p class="placeholder-text">Add your content here</p>
          </div>
        </slot>
      </div>

    </div>
  </div>
</template>

<style scoped>
.quadrant-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #f3e8ff;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

/* Different background colors for different quadrant types */
.quadrant-classes {
  background: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
}

.quadrant-students {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
}

.quadrant-documents {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
}

.quadrant-statistics {
  background: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
}

.card-header {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.add-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
}

/* Classes Content Styles */
.classes-content {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.class-item {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  flex: 1;
}

.class-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.class-teacher {
  font-size: 0.85rem;
  color: #718096;
}

/* Students Content Styles */
.students-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.student-item {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  color: #2d3748;
  text-align: center;
}

/* Documents Content Styles */
.documents-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.document-item {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.85rem;
  color: #2d3748;
}

/* Statistics Content Styles */
.statistics-content {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.85rem;
}

.stats-table th {
  background: #f7fafc;
  padding: 0.5rem;
  text-align: center;
  font-weight: 600;
  color: #2d3748;
  border: 1px solid #e2e8f0;
}

.stats-table td {
  padding: 0.5rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.row-label {
  background: #f7fafc !important;
  font-weight: 500;
  text-align: left !important;
}

/* Default/Placeholder Content */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #a0aec0;
  padding: 2rem;
  height: 100%;
}

.placeholder-text {
  margin: 0;
  font-size: 0.9rem;
  font-style: italic;
}

/* Removed hover effects as requested */

/* Responsive adjustments */
@media (max-width: 768px) {
  .quadrant-card {
    min-height: 220px;
  }

  .card-header {
    padding: 1rem;
  }

  .card-content {
    padding: 1rem;
  }

  .card-title {
    font-size: 1.1rem;
  }

  .placeholder-content {
    padding: 1rem;
  }
}
</style>
