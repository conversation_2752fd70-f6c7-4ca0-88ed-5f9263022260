<script setup>
// Define props for the component
defineProps({
  title: {
    type: String,
    default: 'Quadrant Title'
  },
  description: {
    type: String,
    default: 'Add your content here'
  }
})
</script>

<template>
  <div class="quadrant-card">
    <div class="card-header">
      <h2 class="card-title">{{ title }}</h2>
    </div>
    <div class="card-content">
      <p class="card-description">{{ description }}</p>
      
      <!-- Slot for custom content -->
      <div class="card-body">
        <slot>
          <!-- Default content when no slot is provided -->
          <div class="placeholder-content">
            <div class="placeholder-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <p class="placeholder-text">Click to add content</p>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
.quadrant-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.quadrant-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  text-align: center;
}

.card-content {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-description {
  margin: 0 0 1rem 0;
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.5;
  text-align: center;
}

.card-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #a0aec0;
  padding: 2rem;
}

.placeholder-icon {
  margin-bottom: 1rem;
  opacity: 0.6;
}

.placeholder-text {
  margin: 0;
  font-size: 0.9rem;
  font-style: italic;
}

/* Hover effects for placeholder */
.quadrant-card:hover .placeholder-content {
  color: #667eea;
}

.quadrant-card:hover .placeholder-icon {
  opacity: 1;
  transform: scale(1.1);
  transition: all 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .quadrant-card {
    min-height: 220px;
  }
  
  .card-header {
    padding: 1rem;
  }
  
  .card-content {
    padding: 1rem;
  }
  
  .card-title {
    font-size: 1.1rem;
  }
  
  .placeholder-content {
    padding: 1rem;
  }
}
</style>
