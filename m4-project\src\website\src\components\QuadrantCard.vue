<script setup>
import { ref, reactive, inject, computed } from 'vue'

// Define props for the component
const props = defineProps({
  title: {
    type: String,
    default: 'Quadrant Title'
  },
  addButtonText: {
    type: String,
    default: '+Add Item'
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['classes', 'students', 'documents', 'statistics', 'default'].includes(value)
  }
})

// Inject global state
const selectedClass = inject('selectedClass')
const selectClass = inject('selectClass')

// Reactive data arrays for different quadrant types
const classesData = ref([])
const studentsData = ref([]) // This will store students with classId
const documentsData = ref([])

// Statistics data with reactive subjects and metrics
const statisticsData = reactive({
  subjects: [],
  classAverage: [],
  reachedNorm: [],
  passingPerc: []
})

// Form visibility states
const showClassForm = ref(false)
const showStudentForm = ref(false)
const showDocumentForm = ref(false)
const showSubjectForm = ref(false)

// Form data
const newClass = reactive({
  name: '',
  teacherFirstName: '',
  teacherLastName: ''
})

const newStudent = ref('')

// Computed property to filter students by selected class
const filteredStudents = computed(() => {
  if (!selectedClass.value) return []
  return studentsData.value.filter(student => student.classId === selectedClass.value.id)
})
const newDocument = reactive({
  title: '',
  editor: ''
})

const newSubject = reactive({
  name: '',
  average: '',
  norm: 'yes',
  percentage: ''
})

// Add functions
const addClass = () => {
  if (newClass.name && newClass.teacherFirstName && newClass.teacherLastName) {
    const fullTeacherName = `${newClass.teacherFirstName} ${newClass.teacherLastName}`
    classesData.value.push({
      id: Date.now(),
      name: newClass.name,
      teacher: fullTeacherName
    })
    newClass.name = ''
    newClass.teacherFirstName = ''
    newClass.teacherLastName = ''
    showClassForm.value = false
  }
}

const addStudent = () => {
  if (newStudent.value.trim() && selectedClass.value) {
    studentsData.value.push({
      id: Date.now(),
      name: newStudent.value.trim(),
      classId: selectedClass.value.id
    })
    newStudent.value = ''
    showStudentForm.value = false
  }
}

const addDocument = () => {
  if (newDocument.title && newDocument.editor) {
    const currentDate = new Date().toLocaleDateString('en-GB')
    documentsData.value.push({
      title: newDocument.title,
      lastEdited: currentDate,
      editor: newDocument.editor
    })
    newDocument.title = ''
    newDocument.editor = ''
    showDocumentForm.value = false
  }
}

const addSubject = () => {
  if (newSubject.name && newSubject.average && newSubject.percentage) {
    statisticsData.subjects.push(newSubject.name)
    statisticsData.classAverage.push(newSubject.average)
    statisticsData.reachedNorm.push(newSubject.norm)
    statisticsData.passingPerc.push(newSubject.percentage + '%')
    newSubject.name = ''
    newSubject.average = ''
    newSubject.norm = 'yes'
    newSubject.percentage = ''
    showSubjectForm.value = false
  }
}

// Handle add button clicks
const handleAddClick = () => {
  console.log('Add button clicked, type:', props.type) // Debug log
  switch (props.type) {
    case 'classes':
      console.log('Showing class form') // Debug log
      showClassForm.value = true
      break
    case 'students':
      if (!selectedClass.value) {
        alert('Please select a class first before adding students.')
        return
      }
      showStudentForm.value = true
      break
    case 'documents':
      showDocumentForm.value = true
      break
    case 'statistics':
      showSubjectForm.value = true
      break
  }
}

// Handle class selection
const handleClassSelect = (classItem) => {
  selectClass(classItem)
}

// Cancel forms
const cancelForm = () => {
  showClassForm.value = false
  showStudentForm.value = false
  showDocumentForm.value = false
  showSubjectForm.value = false
}
</script>

<template>
  <div class="quadrant-card" :class="`quadrant-${type}`">
    <div class="card-header">
      <h2 class="card-title">{{ title }}</h2>
      <button v-if="addButtonText" @click="handleAddClick" class="add-button">{{ addButtonText }}</button>
    </div>
    <div class="card-content">

      <!-- Classes Content -->
      <div v-if="type === 'classes'" class="classes-content">
        <!-- Add Class Form -->
        <div v-if="showClassForm" class="add-form">
          <h3>Add New Class</h3>
          <input v-model="newClass.name" placeholder="Class name (e.g., class 5)" class="form-input" />
          <div class="teacher-name-row">
            <input v-model="newClass.teacherFirstName" placeholder="Teacher first name" class="form-input half-width" />
            <input v-model="newClass.teacherLastName" placeholder="Teacher last name" class="form-input half-width" />
          </div>
          <div class="form-buttons">
            <button @click="addClass" class="save-button">Add Class</button>
            <button @click="cancelForm" class="cancel-button">Cancel</button>
          </div>
        </div>

        <!-- Classes List -->
        <div v-if="classesData.length > 0" class="items-grid">
          <div
            v-for="classItem in classesData"
            :key="classItem.id"
            class="class-item"
            :class="{ 'selected': selectedClass && selectedClass.id === classItem.id }"
            @click="handleClassSelect(classItem)"
          >
            <div class="class-name">{{ classItem.name }}</div>
            <div class="class-teacher">{{ classItem.teacher }}</div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="classesData.length === 0 && !showClassForm" class="empty-state">
          <p>No classes added yet. Click "+Add Class" to get started.</p>
        </div>
      </div>

      <!-- Students Content -->
      <div v-else-if="type === 'students'" class="students-content">
        <!-- Add Student Form -->
        <div v-if="showStudentForm" class="add-form">
          <h3>Add New Student</h3>
          <input v-model="newStudent" placeholder="Student name (e.g., John Doe)" class="form-input" />
          <div class="form-buttons">
            <button @click="addStudent" class="save-button">Add Student</button>
            <button @click="cancelForm" class="cancel-button">Cancel</button>
          </div>
        </div>

        <!-- Students List -->
        <div v-if="filteredStudents.length > 0" class="items-grid">
          <div v-for="student in filteredStudents" :key="student.id" class="student-item">
            {{ student.name }}
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!selectedClass && !showStudentForm" class="empty-state">
          <p>Please select a class first to view and add students.</p>
        </div>

        <div v-else-if="selectedClass && filteredStudents.length === 0 && !showStudentForm" class="empty-state">
          <p>No students in {{ selectedClass.name }} yet. Click "+Add Student" to get started.</p>
        </div>
      </div>

      <!-- Documents Content -->
      <div v-else-if="type === 'documents'" class="documents-content">
        <!-- Add Document Form -->
        <div v-if="showDocumentForm" class="add-form">
          <h3>Add New Document</h3>
          <input v-model="newDocument.title" placeholder="Document title (e.g., May 2025)" class="form-input" />
          <input v-model="newDocument.editor" placeholder="Editor name (e.g., Mr. Smith)" class="form-input" />
          <div class="form-buttons">
            <button @click="addDocument" class="save-button">Add Document</button>
            <button @click="cancelForm" class="cancel-button">Cancel</button>
          </div>
        </div>

        <!-- Documents List -->
        <div v-if="documentsData.length > 0" class="items-list">
          <div v-for="(doc, index) in documentsData" :key="index" class="document-item">
            {{ doc.title }} | Last edited {{ doc.lastEdited }} by {{ doc.editor }}
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="documentsData.length === 0 && !showDocumentForm" class="empty-state">
          <p>No documents added yet. Click "+New Document" to get started.</p>
        </div>
      </div>

      <!-- Statistics Content -->
      <div v-else-if="type === 'statistics'" class="statistics-content">
        <!-- Add Subject Form -->
        <div v-if="showSubjectForm" class="add-form">
          <h3>Add New Subject</h3>
          <input v-model="newSubject.name" placeholder="Subject name (e.g., math)" class="form-input" />
          <input v-model="newSubject.average" type="number" placeholder="Class average (e.g., 8)" class="form-input" />
          <select v-model="newSubject.norm" class="form-input">
            <option value="yes">Reached norm: Yes</option>
            <option value="no">Reached norm: No</option>
          </select>
          <input v-model="newSubject.percentage" type="number" placeholder="Passing percentage (e.g., 85)" class="form-input" />
          <div class="form-buttons">
            <button @click="addSubject" class="save-button">Add Subject</button>
            <button @click="cancelForm" class="cancel-button">Cancel</button>
          </div>
        </div>

        <!-- Statistics Table -->
        <div v-if="statisticsData.subjects.length > 0" class="stats-wrapper">
          <table class="stats-table">
            <thead>
              <tr>
                <th></th>
                <th v-for="subject in statisticsData.subjects" :key="subject">{{ subject }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="row-label">class average</td>
                <td v-for="(avg, index) in statisticsData.classAverage" :key="index">{{ avg }}</td>
              </tr>
              <tr>
                <td class="row-label">Reached norm</td>
                <td v-for="(norm, index) in statisticsData.reachedNorm" :key="index">{{ norm }}</td>
              </tr>
              <tr>
                <td class="row-label">passing perc</td>
                <td v-for="(perc, index) in statisticsData.passingPerc" :key="index">{{ perc }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div v-if="statisticsData.subjects.length === 0 && !showSubjectForm" class="empty-state">
          <p>No subjects added yet. Click the title to add subjects and view statistics.</p>
        </div>
      </div>

      <!-- Default Content -->
      <div v-else class="default-content">
        <slot>
          <div class="placeholder-content">
            <p class="placeholder-text">Add your content here</p>
          </div>
        </slot>
      </div>

    </div>
  </div>
</template>

<style scoped>
.quadrant-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #f3e8ff;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

/* Different background colors for different quadrant types */
.quadrant-classes {
  background: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
}

.quadrant-students {
  background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
}

.quadrant-documents {
  background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
}

.quadrant-statistics {
  background: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
}

.card-header {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
}

.add-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
}

/* Form Styles */
.add-form {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.add-form h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.teacher-name-row {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.half-width {
  flex: 1;
  margin-bottom: 0;
}

.form-buttons {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.save-button {
  background: #48bb78;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.save-button:hover {
  background: #38a169;
}

.cancel-button {
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.3s ease;
}

.cancel-button:hover {
  background: #cbd5e0;
}

/* Empty State Styles */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 150px;
  text-align: center;
  color: #a0aec0;
  font-style: italic;
}

.empty-state p {
  margin: 0;
}

/* Content Grid/List Styles */
.items-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Classes Content Styles */
.class-item {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.class-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.class-item.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.class-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.class-teacher {
  font-size: 0.85rem;
  color: #718096;
}

/* Students Content Styles */
.students-content .items-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.student-item {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  color: #2d3748;
  text-align: center;
}

/* Documents Content Styles */
.document-item {
  background: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 0.85rem;
  color: #2d3748;
}

/* Statistics Content Styles */
.stats-wrapper {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.85rem;
}

.stats-table th {
  background: #f7fafc;
  padding: 0.5rem;
  text-align: center;
  font-weight: 600;
  color: #2d3748;
  border: 1px solid #e2e8f0;
}

.stats-table td {
  padding: 0.5rem;
  text-align: center;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.row-label {
  background: #f7fafc !important;
  font-weight: 500;
  text-align: left !important;
}

/* Default/Placeholder Content */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #a0aec0;
  padding: 2rem;
  height: 100%;
}

.placeholder-text {
  margin: 0;
  font-size: 0.9rem;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .quadrant-card {
    min-height: 220px;
  }

  .card-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .card-content {
    padding: 0.75rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .add-button {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .items-grid {
    flex-direction: column;
  }

  .students-content .items-grid {
    grid-template-columns: 1fr;
  }

  .form-buttons {
    flex-direction: column;
  }

  .teacher-name-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .half-width {
    margin-bottom: 0.75rem;
  }

  .add-form {
    padding: 1rem;
  }

  .stats-table {
    font-size: 0.75rem;
  }

  .stats-table th,
  .stats-table td {
    padding: 0.3rem;
  }
}
</style>
