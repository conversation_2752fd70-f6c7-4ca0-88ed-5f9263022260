<script setup>
import { ref, provide } from 'vue'
import QuadrantCard from './components/QuadrantCard.vue'

// Global state for selected class
const selectedClass = ref(null)

// Provide the selected class to all child components
provide('selectedClass', selectedClass)

// Function to handle class selection
const selectClass = (classData) => {
  selectedClass.value = classData
}

// Provide the select function to child components
provide('selectClass', selectClass)
</script>

<template>
  <div class="app-container">
    <!-- Top Navigation Bar -->
    <header class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container-fluid">
        <div class="navbar-brand">
          <h1 class="h4 mb-0">topicus</h1>
        </div>
        <nav class="d-flex">
          <!-- Notification Icon -->
          <button class="btn btn-outline-light me-2" aria-label="Notifications">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
            </svg>
          </button>
          <!-- Profile Icon -->
          <button class="btn btn-outline-light" aria-label="Profile">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </svg>
          </button>
        </nav>
      </div>
    </header>

    <!-- Main Content Area with 4 Quadrants -->
    <main class="container-fluid py-4 bg-light min-vh-100">
      <div class="row g-3">
        <div class="col-md-6">
          <QuadrantCard
            title="Classes"
            add-button-text="+Add Class"
            type="classes"
            class="h-100"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Students ${selectedClass.name}` : 'Students - Select a class'"
            add-button-text="+Add Student"
            type="students"
            class="h-100"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Documents ${selectedClass.name}` : 'Documents - Select a class'"
            add-button-text="+New Document"
            type="documents"
            class="h-100"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `${selectedClass.name} ${selectedClass.teacher}` : 'Statistics - Select a class'"
            type="statistics"
            class="h-100"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
}
</style>
