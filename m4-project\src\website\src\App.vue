<script setup>
import QuadrantCard from './components/QuadrantCard.vue'
</script>

<template>
  <div class="app-container">
    <!-- Top Navigation Bar -->
    <header class="top-bar">
      <div class="nav-content">
        <div class="logo-section">
          <h1 class="app-title">topicus</h1>
        </div>
        <nav class="nav-icons">
          <!-- Notification Icon -->
          <button class="icon-button" aria-label="Notifications">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 8A6 6 0 0 0 6 8C6 15 3 17 3 17H21S18 15 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M13.73 21A2 2 0 0 1 10.27 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <!-- Profile Icon -->
          <button class="icon-button" aria-label="Profile">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </nav>
      </div>
    </header>

    <!-- Main Content Area with 4 Quadrants -->
    <main class="main-content">
      <div class="quadrants-container">
        <QuadrantCard
          title="Quadrant 1"
          description="This is the first quadrant where you can add your content."
          class="quadrant-1"
        />
        <QuadrantCard
          title="Quadrant 2"
          description="This is the second quadrant for additional content."
          class="quadrant-2"
        />
        <QuadrantCard
          title="Quadrant 3"
          description="This is the third quadrant for more features."
          class="quadrant-3"
        />
        <QuadrantCard
          title="Quadrant 4"
          description="This is the fourth quadrant for final content."
          class="quadrant-4"
        />
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Top Bar Styles */
.top-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-icons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.icon-button {
  background: none;
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 1rem;
  background-color: #f8fafc;
}

.quadrants-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  min-height: 600px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-content {
    padding: 0 1rem;
  }

  .nav-icons {
    gap: 0.75rem;
  }

  .quadrants-container {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .main-content {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.25rem;
  }

  .icon-button {
    padding: 0.4rem;
  }
}
</style>
