<script setup>
import QuadrantCard from './components/QuadrantCard.vue'
</script>

<template>
  <div class="app-container">
    <!-- Top Navigation Bar -->
    <header class="top-bar">
      <div class="nav-content">
        <div class="logo-section">
          <img alt="Vue logo" class="logo" src="./assets/logo.svg" width="40" height="40" />
          <h1 class="app-title">My Vue App</h1>
        </div>
        <nav class="nav-links">
          <a href="#" class="nav-link">Home</a>
          <a href="#" class="nav-link">About</a>
          <a href="#" class="nav-link">Services</a>
          <a href="#" class="nav-link">Contact</a>
        </nav>
      </div>
    </header>

    <!-- Main Content Area with 4 Quadrants -->
    <main class="main-content">
      <div class="quadrants-container">
        <QuadrantCard
          title="Quadrant 1"
          description="This is the first quadrant where you can add your content."
          class="quadrant-1"
        />
        <QuadrantCard
          title="Quadrant 2"
          description="This is the second quadrant for additional content."
          class="quadrant-2"
        />
        <QuadrantCard
          title="Quadrant 3"
          description="This is the third quadrant for more features."
          class="quadrant-3"
        />
        <QuadrantCard
          title="Quadrant 4"
          description="This is the fourth quadrant for final content."
          class="quadrant-4"
        />
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Top Bar Styles */
.top-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  border-radius: 50%;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 2rem;
  background-color: #f8fafc;
}

.quadrants-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  min-height: 600px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-content {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .quadrants-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  .main-content {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
  }

  .app-title {
    font-size: 1.25rem;
  }
}
</style>
